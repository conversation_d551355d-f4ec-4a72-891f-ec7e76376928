use windows::{
    Win32::{Foundation::*, UI::WindowsAndMessaging::*, Graphics::Gdi::*},
    core::*,
};

/// 子窗口枚举上下文
struct ChildEnumContext {
    children: Vec<WindowInfo>,
    parent_level: u32,
}

/// 窗口信息结构体
#[derive(Debug, Clone)]
pub struct WindowInfo {
    pub hwnd: HWND,
    pub rect: RECT,
    pub title: String,
    pub class_name: String,
    pub is_visible: bool,
    pub is_minimized: bool,
    pub parent_hwnd: Option<HWND>,
    pub window_level: u32,  // 窗口层级，0为顶级窗口
    pub children: Vec<WindowInfo>,  // 子窗口列表
    pub is_child_window: bool,  // 是否为子窗口
}

impl WindowInfo {
    /// 检查点是否在窗口内
    pub fn contains_point(&self, x: i32, y: i32) -> bool {
        x >= self.rect.left && x <= self.rect.right && y >= self.rect.top && y <= self.rect.bottom
    }

    /// 创建新的窗口信息
    pub fn new(hwnd: HWND, parent_hwnd: Option<HWND>, window_level: u32) -> Result<Self> {
        unsafe {
            // 获取窗口矩形
            let mut rect = RECT::default();
            GetWindowRect(hwnd, &mut rect)?;

            // 获取窗口标题
            let mut title_buffer = [0u16; 256];
            let title_len = GetWindowTextW(hwnd, &mut title_buffer);
            let title = if title_len > 0 {
                String::from_utf16_lossy(&title_buffer[..title_len as usize])
            } else {
                String::new()
            };

            // 获取窗口类名
            let mut class_buffer = [0u16; 256];
            let class_len = GetClassNameW(hwnd, &mut class_buffer);
            let class_name = if class_len > 0 {
                String::from_utf16_lossy(&class_buffer[..class_len as usize])
            } else {
                String::new()
            };

            // 检查窗口状态
            let is_visible = IsWindowVisible(hwnd).as_bool();
            let is_minimized = IsIconic(hwnd).as_bool();
            let is_child_window = parent_hwnd.is_some();

            Ok(WindowInfo {
                hwnd,
                rect,
                title,
                class_name,
                is_visible,
                is_minimized,
                parent_hwnd,
                window_level,
                children: Vec::new(),
                is_child_window,
            })
        }
    }

    /// 递归查找包含指定点的最小子窗口
    pub fn find_deepest_window_at_point(&self, x: i32, y: i32) -> Option<&WindowInfo> {
        // 首先检查当前窗口是否包含该点
        if !self.contains_point(x, y) {
            return None;
        }

        // 递归检查子窗口，找到最深层的匹配窗口
        for child in &self.children {
            if let Some(deepest) = child.find_deepest_window_at_point(x, y) {
                return Some(deepest);
            }
        }

        // 如果没有子窗口包含该点，返回当前窗口
        Some(self)
    }

    /// 获取所有子窗口（扁平化列表）
    pub fn get_all_children(&self) -> Vec<&WindowInfo> {
        let mut result = Vec::new();
        self.collect_children(&mut result);
        result
    }

    /// 递归收集所有子窗口
    fn collect_children<'a>(&'a self, result: &mut Vec<&'a WindowInfo>) {
        for child in &self.children {
            result.push(child);
            child.collect_children(result);
        }
    }
}

/// 窗口检测器
#[derive(Debug)]
pub struct WindowDetector {
    windows: Vec<WindowInfo>,
    all_windows: Vec<WindowInfo>,  // 包含所有子窗口的扁平化列表
    current_highlighted_window: Option<usize>,
    enable_child_detection: bool,  // 是否启用子窗口检测
}

impl WindowDetector {
    pub fn new() -> Self {
        Self {
            windows: Vec::new(),
            all_windows: Vec::new(),
            current_highlighted_window: None,
            enable_child_detection: true,  // 重新启用子窗口检测
        }
    }

    /// 设置是否启用子窗口检测
    pub fn set_child_detection(&mut self, enabled: bool) {
        self.enable_child_detection = enabled;
    }

    /// 检查是否启用了子窗口检测
    pub fn is_child_detection_enabled(&self) -> bool {
        self.enable_child_detection
    }

    /// 调试：打印检测到的窗口信息
    pub fn debug_print_windows(&self) {
        println!("=== 检测到的窗口信息 ===");
        println!("顶级窗口数量: {}", self.windows.len());
        println!("所有窗口数量: {}", self.all_windows.len());

        for (i, window) in self.all_windows.iter().enumerate() {
            println!("窗口 {}: {} [{}] - 大小: {}x{}",
                i,
                window.title,
                window.class_name,
                window.rect.right - window.rect.left,
                window.rect.bottom - window.rect.top
            );
        }
        println!("========================");
    }

    /// 获取所有活动窗口
    pub fn refresh_windows(&mut self) -> Result<()> {
        self.windows.clear();
        self.all_windows.clear();

        unsafe {
            // 使用EnumWindows枚举所有顶级窗口
            EnumWindows(
                Some(enum_windows_proc),
                LPARAM(&mut self.windows as *mut _ as isize),
            )?;
        }

        // 过滤掉不需要的顶级窗口
        self.windows.retain(|window| {
            window.is_visible
                && !window.is_minimized
                && !window.title.is_empty()
                && window.rect.right > window.rect.left
                && window.rect.bottom > window.rect.top
                && !is_system_window(&window.class_name)
                && window.class_name != "sc_windows_main"  // 排除截图工具自己的窗口
        });

        // 如果启用了子窗口检测，枚举每个顶级窗口的子窗口
        if self.enable_child_detection {
            let mut windows_with_children = Vec::new();
            let windows_to_process: Vec<WindowInfo> = self.windows.drain(..).collect();

            for mut window in windows_to_process {
                // 枚举子窗口
                if let Err(_) = self.enumerate_child_windows(&mut window) {
                    // 如果枚举子窗口失败，仍然保留父窗口
                }
                windows_with_children.push(window);
            }

            self.windows = windows_with_children;

            // 创建扁平化的所有窗口列表（包括子窗口）
            self.build_flat_window_list();
        }

        // 调试输出
        println!("窗口检测完成 - 顶级窗口: {}, 总窗口: {}", self.windows.len(), self.all_windows.len());

        // 详细输出所有窗口的信息
        for (i, window) in self.all_windows.iter().enumerate() {
            println!("  窗口{}: '{}' [{}] 大小:{}x{} 句柄:{:?}",
                i,
                window.title,
                window.class_name,
                window.rect.right - window.rect.left,
                window.rect.bottom - window.rect.top,
                window.hwnd.0
            );
        }

        Ok(())
    }

    /// 枚举指定窗口的所有子窗口
    fn enumerate_child_windows(&self, parent_window: &mut WindowInfo) -> Result<()> {
        unsafe {
            let mut child_context = ChildEnumContext {
                children: Vec::new(),
                parent_level: parent_window.window_level,
            };

            let _ = EnumChildWindows(
                Some(parent_window.hwnd),
                Some(enum_child_windows_proc),
                LPARAM(&mut child_context as *mut _ as isize),
            );

            // 递归处理每个子窗口
            for mut child in child_context.children {
                // 过滤有意义的子窗口
                if self.is_meaningful_child_window(&child) {
                    // 递归枚举子窗口的子窗口
                    self.enumerate_child_windows(&mut child)?;
                    parent_window.children.push(child);
                }
            }
        }

        Ok(())
    }

    /// 构建扁平化的窗口列表
    fn build_flat_window_list(&mut self) {
        self.all_windows.clear();

        for window in &self.windows {
            self.all_windows.push(window.clone());
            Self::collect_all_children_static(&mut self.all_windows, window);
        }
    }

    /// 静态方法：递归收集所有子窗口到扁平化列表
    fn collect_all_children_static(all_windows: &mut Vec<WindowInfo>, window: &WindowInfo) {
        for child in &window.children {
            all_windows.push(child.clone());
            Self::collect_all_children_static(all_windows, child);
        }
    }

    /// 判断子窗口是否有意义（值得检测）
    fn is_meaningful_child_window(&self, window: &WindowInfo) -> bool {
        // 必须可见且有有效的矩形区域
        if !window.is_visible || window.rect.right <= window.rect.left || window.rect.bottom <= window.rect.top {
            return false;
        }

        // 检查窗口大小，过滤掉太小的窗口（可能是装饰性元素）
        let width = window.rect.right - window.rect.left;
        let height = window.rect.bottom - window.rect.top;

        // 放宽最小尺寸要求，让更多子窗口被检测到
        if width < 3 || height < 3 {
            return false;
        }

        // 检查类名，保留有意义的控件类型
        if is_meaningful_control_class(&window.class_name) {
            return true;
        }

        // 如果有标题文本，也认为是有意义的
        if !window.title.is_empty() && window.title.trim().len() > 0 {
            return true;
        }

        // 对于浏览器相关窗口，更宽松的条件
        if is_browser_related(&window.class_name) {
            // 浏览器内部的控件，即使很小也可能有意义
            if width > 5 && height > 5 {
                return true;
            }
        }

        // 对于一般窗口，放宽尺寸要求
        if width > 15 && height > 10 {
            return true;
        }

        false
    }

    /// 根据鼠标位置获取当前应该高亮的窗口（支持子窗口检测）
    pub fn get_window_at_point(&mut self, x: i32, y: i32) -> Option<&WindowInfo> {
        if self.enable_child_detection {
            // 先尝试动态添加窗口，然后再获取
            self.try_add_dynamic_window_at_point(x, y);
            self.get_precise_window_at_point(x, y)
        } else {
            self.get_top_level_window_at_point(x, y)
        }
    }

    /// 尝试在指定位置动态添加窗口
    fn try_add_dynamic_window_at_point(&mut self, x: i32, y: i32) {
        unsafe {
            let point = POINT { x, y };
            let hwnd_at_point = WindowFromPoint(point);

            if hwnd_at_point.0.is_null() {
                return;
            }

            println!("尝试动态添加窗口句柄: {:?}", hwnd_at_point.0);

            // 检查是否已经存在
            for window in &self.all_windows {
                if window.hwnd == hwnd_at_point {
                    println!("窗口已存在，无需添加");
                    return; // 已存在，不需要添加
                }
            }

            // 尝试创建新的窗口信息
            if let Ok(dynamic_window) = WindowInfo::new(hwnd_at_point, None, 0) {
                println!("创建窗口信息成功: '{}' [{}]", dynamic_window.title, dynamic_window.class_name);

                // 放宽条件，只要不是截图工具自己的窗口就添加
                if dynamic_window.class_name != "sc_windows_main" {
                    println!("动态添加窗口: '{}' [{}]", dynamic_window.title, dynamic_window.class_name);
                    self.all_windows.push(dynamic_window);
                } else {
                    println!("跳过截图工具自己的窗口");
                }
            } else {
                println!("创建窗口信息失败");
            }
        }
    }

    /// 获取精确的窗口（包括子窗口）
    fn get_precise_window_at_point(&mut self, x: i32, y: i32) -> Option<&WindowInfo> {
        unsafe {
            let point = POINT { x, y };
            let hwnd_at_point = WindowFromPoint(point);

            if hwnd_at_point.0.is_null() {
                self.current_highlighted_window = None;
                return None;
            }

            println!("WindowFromPoint找到窗口: {:?}", hwnd_at_point.0);

            // 首先直接在所有窗口中查找匹配的窗口句柄
            for (index, window) in self.all_windows.iter().enumerate() {
                if window.hwnd == hwnd_at_point {
                    self.current_highlighted_window = Some(index);
                    println!("找到现有窗口: '{}' [{}]", window.title, window.class_name);
                    return Some(window);
                }
            }

            // 如果没找到直接匹配，使用ChildWindowFromPoint进一步精确定位
            let mut current_hwnd = hwnd_at_point;
            let mut deepest_hwnd = hwnd_at_point;

            // 向上查找到顶级窗口
            let mut top_level_hwnd = hwnd_at_point;
            loop {
                match GetParent(top_level_hwnd) {
                    Ok(parent) => {
                        if parent.0.is_null() {
                            break;
                        }
                        top_level_hwnd = parent;
                    }
                    Err(_) => break,
                }
            }

            // 从顶级窗口开始，使用ChildWindowFromPoint精确查找
            current_hwnd = top_level_hwnd;
            let mut depth = 0;
            loop {
                depth += 1;
                if depth > 10 { // 防止无限循环
                    break;
                }

                // 将屏幕坐标转换为窗口客户区坐标
                let mut client_point = point;
                if ScreenToClient(current_hwnd, &mut client_point).as_bool() {
                    let child_hwnd = ChildWindowFromPoint(current_hwnd, client_point);
                    if child_hwnd.0.is_null() || child_hwnd == current_hwnd {
                        break;
                    }
                    deepest_hwnd = child_hwnd;
                    current_hwnd = child_hwnd;
                } else {
                    break;
                }
            }

            // 在所有窗口列表中查找匹配的窗口
            println!("查找deepest_hwnd: {:?}", deepest_hwnd.0);
            for (index, window) in self.all_windows.iter().enumerate() {
                if window.hwnd == deepest_hwnd {
                    self.current_highlighted_window = Some(index);
                    println!("找到子窗口: '{}' [{}]", window.title, window.class_name);
                    return Some(window);
                }
            }

            // 如果没找到deepest_hwnd，尝试直接匹配原始的hwnd_at_point
            println!("未找到deepest_hwnd，尝试原始hwnd");
            for (index, window) in self.all_windows.iter().enumerate() {
                if window.hwnd == hwnd_at_point {
                    self.current_highlighted_window = Some(index);
                    println!("找到原始窗口: '{}' [{}]", window.title, window.class_name);
                    return Some(window);
                }
            }

            self.current_highlighted_window = None;
            None
        }
    }

    /// 获取顶级窗口（原有逻辑）
    fn get_top_level_window_at_point(&mut self, x: i32, y: i32) -> Option<&WindowInfo> {
        // 找到鼠标位置下的所有窗口
        let mut matching_windows = Vec::new();
        for (index, window) in self.windows.iter().enumerate() {
            if window.contains_point(x, y) {
                matching_windows.push((index, window));
            }
        }

        if matching_windows.is_empty() {
            self.current_highlighted_window = None;
            return None;
        }

        // 如果只有一个窗口，直接返回
        if matching_windows.len() == 1 {
            let (index, window) = matching_windows[0];
            self.current_highlighted_window = Some(index);
            return Some(window);
        }

        // 如果有多个窗口，使用WindowFromPoint来确定最顶层的窗口
        unsafe {
            let point = POINT { x, y };
            let hwnd_at_point = WindowFromPoint(point);

            if !hwnd_at_point.0.is_null() {
                // 获取顶级窗口
                let mut top_level_hwnd = hwnd_at_point;
                loop {
                    match GetParent(top_level_hwnd) {
                        Ok(parent) => {
                            if parent.0.is_null() {
                                break;
                            }
                            top_level_hwnd = parent;
                        }
                        Err(_) => break,
                    }
                }

                // 在匹配的窗口中查找对应的窗口
                for (index, window) in &matching_windows {
                    if window.hwnd == top_level_hwnd {
                        self.current_highlighted_window = Some(*index);
                        return Some(window);
                    }
                }
            }
        }

        // 如果WindowFromPoint失败，返回第一个匹配的窗口（通常是最小的）
        let (index, window) = matching_windows[0];
        self.current_highlighted_window = Some(index);
        Some(window)
    }

    /// 获取当前高亮的窗口
    pub fn get_current_highlighted_window(&self) -> Option<&WindowInfo> {
        if let Some(index) = self.current_highlighted_window {
            self.windows.get(index)
        } else {
            None
        }
    }

    /// 获取所有窗口（顶级窗口）
    pub fn get_all_windows(&self) -> &Vec<WindowInfo> {
        &self.windows
    }

    /// 获取所有窗口（包括子窗口的扁平化列表）
    pub fn get_all_windows_flat(&self) -> &Vec<WindowInfo> {
        &self.all_windows
    }
}

/// EnumWindows的回调函数
unsafe extern "system" fn enum_windows_proc(hwnd: HWND, lparam: LPARAM) -> BOOL {
    unsafe {
        let windows = &mut *(lparam.0 as *mut Vec<WindowInfo>);

        // 使用新的WindowInfo构造函数
        match WindowInfo::new(hwnd, None, 0) {
            Ok(mut window_info) => {
                // 修正全屏窗口的矩形坐标，确保不超出屏幕边界
                let screen_width = GetSystemMetrics(SM_CXSCREEN);
                let screen_height = GetSystemMetrics(SM_CYSCREEN);

                // 限制窗口矩形在屏幕范围内
                window_info.rect.left = window_info.rect.left.max(0);
                window_info.rect.top = window_info.rect.top.max(0);
                window_info.rect.right = window_info.rect.right.min(screen_width);
                window_info.rect.bottom = window_info.rect.bottom.min(screen_height);

                windows.push(window_info);
            }
            Err(_) => {
                // 如果创建WindowInfo失败，继续枚举下一个窗口
            }
        }

        TRUE // 继续枚举
    }
}

/// EnumChildWindows的回调函数
unsafe extern "system" fn enum_child_windows_proc(hwnd: HWND, lparam: LPARAM) -> BOOL {
    unsafe {
        let context = &mut *(lparam.0 as *mut ChildEnumContext);

        // 获取父窗口句柄
        let parent_hwnd = match GetParent(hwnd) {
            Ok(parent) => Some(parent),
            Err(_) => None,
        };

        // 使用新的WindowInfo构造函数
        match WindowInfo::new(hwnd, parent_hwnd, context.parent_level + 1) {
            Ok(window_info) => {
                context.children.push(window_info);
            }
            Err(_) => {
                // 如果创建WindowInfo失败，继续枚举下一个窗口
            }
        }

        TRUE // 继续枚举
    }
}

/// 检查是否为系统窗口（需要过滤掉的窗口）
fn is_system_window(class_name: &str) -> bool {
    const SYSTEM_CLASSES: &[&str] = &[
        "Shell_TrayWnd",              // 任务栏
        "DV2ControlHost",             // 系统控件
        "MsgrIMEWindowClass",         // 输入法
        "SysShadow",                  // 系统阴影
        "Progman",                    // 桌面
        "WorkerW",                    // 桌面工作区
        "Windows.UI.Core.CoreWindow", // UWP应用核心窗口
        "ApplicationFrameWindow",     // UWP应用框架
        "ForegroundStaging",          // 前台暂存
        "MultitaskingViewFrame",      // 多任务视图
        "EdgeUiInputTopWndClass",     // Edge UI
        "NativeHWNDHost",             // 原生HWND主机
    ];

    SYSTEM_CLASSES
        .iter()
        .any(|&sys_class| class_name.contains(sys_class))
}

/// 检查是否为有意义的控件类型
fn is_meaningful_control_class(class_name: &str) -> bool {
    const MEANINGFUL_CLASSES: &[&str] = &[
        // 标准Windows控件
        "Button",                     // 按钮
        "Edit",                       // 文本框
        "Static",                     // 静态文本
        "ComboBox",                   // 下拉框
        "ListBox",                    // 列表框
        "SysListView32",              // 列表视图
        "SysTreeView32",              // 树形视图
        "SysTabControl32",            // 标签控件
        "ToolbarWindow32",            // 工具栏
        "msctls_statusbar32",         // 状态栏
        "msctls_progress32",          // 进度条
        "ScrollBar",                  // 滚动条
        "RichEdit",                   // 富文本编辑器
        "RICHEDIT50W",                // 富文本编辑器

        // 浏览器相关
        "Chrome_RenderWidgetHostHWND", // Chrome渲染窗口
        "Chrome_WidgetWin_1",         // Chrome控件窗口
        "MozillaWindowClass",         // Firefox窗口
        "IEFrame",                    // Internet Explorer
        "CabinetWClass",              // 文件资源管理器
        "ExploreWClass",              // 资源管理器

        // 办公软件
        "_WwG",                       // Word文档窗口
        "XLMAIN",                     // Excel主窗口
        "PPTFrameClass",              // PowerPoint框架
        "OpusApp",                    // Word应用窗口

        // 其他常见应用
        "Notepad",                    // 记事本
        "MSPaintApp",                 // 画图
        "CalcFrame",                  // 计算器
        "MediaPlayerClassicW",        // 媒体播放器
        "VLC DirectX video output",   // VLC播放器

        // 开发工具
        "Afx:",                       // MFC应用
        "Qt",                         // Qt应用
        "GLFW",                       // OpenGL窗口
        "SDL_app",                    // SDL应用

        // 自定义窗口类（通常包含应用名称）
        "HwndWrapper",                // WPF应用包装器
        "WindowsForms10",             // Windows Forms
        "SunAwtFrame",                // Java Swing
        "SunAwtDialog",               // Java对话框
    ];

    // 检查完全匹配
    if MEANINGFUL_CLASSES.iter().any(|&class| class_name == class) {
        return true;
    }

    // 检查部分匹配（对于包含特定字符串的类名）
    const PARTIAL_MATCHES: &[&str] = &[
        "Chrome_",
        "Mozilla",
        "Qt",
        "Afx:",
        "WindowsForms",
        "SunAwt",
        "HwndWrapper",
        "DirectUIHWND",  // 现代UI控件
        "Button",
        "Edit",
        "Static",
        "ComboBox",
        "ListBox",
        "TreeView",
        "TabControl",
        "Toolbar",
        "WebView",       // WebView控件
        "Browser",       // 浏览器相关
        "Widget",        // 控件
        "Render",        // 渲染窗口
        "Frame",         // 框架窗口
        "Panel",         // 面板
        "View",          // 视图
        "Control",       // 控件
        "msctls_",       // 系统控件
        "Sys",           // 系统控件前缀
    ];

    PARTIAL_MATCHES.iter().any(|&pattern| class_name.contains(pattern))
}

/// 检查是否为浏览器相关的窗口
fn is_browser_related(class_name: &str) -> bool {
    const BROWSER_CLASSES: &[&str] = &[
        "Chrome_RenderWidgetHostHWND",
        "Chrome_WidgetWin_0",
        "Chrome_WidgetWin_1",
        "Chrome_WidgetWin_2",
        "MozillaWindowClass",
        "MozillaDialogClass",
        "MozillaDropShadowWindowClass",
        "IEFrame",
        "Internet Explorer_Server",
        "Shell DocObject View",
        "EdgeWebView",
        "WebView2",
        "CefBrowserWindow",
        "ElectronWebView",
    ];

    BROWSER_CLASSES.iter().any(|&browser_class| {
        class_name.contains(browser_class) || class_name == browser_class
    })
}

/// 获取窗口在屏幕上的实际可见区域（考虑被其他窗口遮挡的情况）
pub fn get_visible_window_region(hwnd: HWND) -> Result<Vec<RECT>> {
    unsafe {
        let mut window_rect = RECT::default();
        GetWindowRect(hwnd, &mut window_rect)?;

        // 简化版本：直接返回窗口矩形
        // 在实际应用中，可以使用更复杂的算法来计算可见区域
        Ok(vec![window_rect])
    }
}
